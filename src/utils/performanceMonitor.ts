/**
 * Performance monitoring utilities for tracking hook usage and React Query performance
 */

interface HookCallTracker {
  hookName: string;
  callCount: number;
  lastCalled: Date;
  certificateIds: Set<string>;
}

class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private hookCalls: Map<string, HookCallTracker> = new Map();
  private queryMetrics: Map<string, { fetchCount: number; cacheHits: number }> = new Map();

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Track hook usage
   */
  trackHookCall(hookName: string, certificateId: string | null = null): void {
    const key = hookName;
    const existing = this.hookCalls.get(key);
    
    if (existing) {
      existing.callCount++;
      existing.lastCalled = new Date();
      if (certificateId) {
        existing.certificateIds.add(certificateId);
      }
    } else {
      this.hookCalls.set(key, {
        hookName,
        callCount: 1,
        lastCalled: new Date(),
        certificateIds: certificateId ? new Set([certificateId]) : new Set()
      });
    }

    // Log performance metrics in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`📊 Hook Performance: ${hookName} called ${this.getHookCallCount(hookName)} times`);
    }
  }

  /**
   * Track React Query metrics
   */
  trackQueryMetric(queryKey: string, isCacheHit: boolean): void {
    const existing = this.queryMetrics.get(queryKey);
    
    if (existing) {
      existing.fetchCount++;
      if (isCacheHit) {
        existing.cacheHits++;
      }
    } else {
      this.queryMetrics.set(queryKey, {
        fetchCount: 1,
        cacheHits: isCacheHit ? 1 : 0
      });
    }
  }

  /**
   * Get hook call count
   */
  getHookCallCount(hookName: string): number {
    return this.hookCalls.get(hookName)?.callCount || 0;
  }

  /**
   * Get total hook calls across all hooks
   */
  getTotalHookCalls(): number {
    return Array.from(this.hookCalls.values()).reduce((sum, tracker) => sum + tracker.callCount, 0);
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): {
    hookCalls: { [hookName: string]: number };
    totalHookCalls: number;
    queryMetrics: { [queryKey: string]: { fetchCount: number; cacheHitRate: number } };
    useCertificateFilesInstances: number;
  } {
    const hookCalls: { [hookName: string]: number } = {};
    this.hookCalls.forEach((tracker, hookName) => {
      hookCalls[hookName] = tracker.callCount;
    });

    const queryMetrics: { [queryKey: string]: { fetchCount: number; cacheHitRate: number } } = {};
    this.queryMetrics.forEach((metrics, queryKey) => {
      queryMetrics[queryKey] = {
        fetchCount: metrics.fetchCount,
        cacheHitRate: metrics.cacheHits / metrics.fetchCount
      };
    });

    return {
      hookCalls,
      totalHookCalls: this.getTotalHookCalls(),
      queryMetrics,
      useCertificateFilesInstances: this.getHookCallCount('useCertificateFiles')
    };
  }

  /**
   * Reset all metrics
   */
  reset(): void {
    this.hookCalls.clear();
    this.queryMetrics.clear();
  }

  /**
   * Log performance summary to console
   */
  logPerformanceSummary(): void {
    const summary = this.getPerformanceSummary();
    console.group('🚀 Performance Summary');
    console.log('Hook Calls:', summary.hookCalls);
    console.log('Total Hook Calls:', summary.totalHookCalls);
    console.log('useCertificateFiles Instances:', summary.useCertificateFilesInstances);
    console.log('Query Metrics:', summary.queryMetrics);
    console.groupEnd();
  }
}

export const performanceMonitor = PerformanceMonitor.getInstance();

/**
 * Hook to track performance in development
 */
export const usePerformanceTracking = (hookName: string, certificateId?: string | null) => {
  if (process.env.NODE_ENV === 'development') {
    performanceMonitor.trackHookCall(hookName, certificateId);
  }
};
