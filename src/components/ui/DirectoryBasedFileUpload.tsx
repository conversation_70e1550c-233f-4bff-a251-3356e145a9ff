import { useState, memo, useCallback, useMemo } from 'react';
import { supabase } from '../../lib/supabase';
import { useCertificateFiles } from '../../hooks/useCertificateFiles';
import { FileWithSignedUrl } from './FileWithSignedUrl';
import { type StorageFileInfo } from '../../utils/fileUtils';

interface DirectoryBasedFileUploadProps {
  certificateId: string | null;
  fieldName: 'verbrauchsrechnung1' | 'verbrauchsrechnung2' | 'verbrauchsrechnung3' | 'gebaeudebild' | 'grundriss' | 'typenschild';
  label: string;
  accept?: string;
  multiple?: boolean;
  required?: boolean;
  maxFiles?: number;
  maxFileSize?: number;
  maxTotalSize?: number;
}

/**
 * Directory-based file upload component that doesn't rely on database storage
 * Files are managed purely through the storage directory structure
 * Memoized to prevent unnecessary re-renders
 */
export const DirectoryBasedFileUpload = memo(({
  certificateId,
  fieldName,
  label,
  accept = '.pdf,.jpg,.jpeg,.png,.webp',
  multiple = true,
  required = false,
  maxFiles = 5,
  maxFileSize = 5 * 1024 * 1024, // 5MB
  maxTotalSize = 20 * 1024 * 1024 // 20MB
}: DirectoryBasedFileUploadProps) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{ [fileName: string]: number }>({});
  const [uploadErrors, setUploadErrors] = useState<string[]>([]);
  const [uploadingFiles, setUploadingFiles] = useState<Set<string>>(new Set()); // Track files currently being uploaded

  const {
    getFilesForField,
    deleteFileFromStorage,
    checkFileExistsWithRefresh,
    refreshFiles,
    isDeleting
  } = useCertificateFiles(certificateId);

  // Get files for this specific field
  const fieldFiles = getFilesForField(fieldName);

  // File validation constants
  const ALLOWED_FILE_TYPES = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

  // Image dimension requirements for building images (6cm x 4cm at 60px/cm = 360x240px minimum)
  const IMAGE_DIMENSION_REQUIREMENTS = {
    minWidth: 360,   // 6cm * 60px/cm
    minHeight: 240,  // 4cm * 60px/cm
    maxWidth: 4000,  // Maximum reasonable width
    maxHeight: 3000  // Maximum reasonable height
  };

  // Validate image dimensions
  const validateImageDimensions = (file: File): Promise<string | null> => {
    return new Promise((resolve) => {
      // Only validate images for gebaeudebild field
      if (fieldName !== 'gebaeudebild' || !file.type.startsWith('image/')) {
        resolve(null);
        return;
      }

      const img = new Image();
      const url = URL.createObjectURL(file);

      img.onload = () => {
        URL.revokeObjectURL(url);

        const { width, height } = img;
        const { minWidth, minHeight, maxWidth, maxHeight } = IMAGE_DIMENSION_REQUIREMENTS;

        if (width < minWidth || height < minHeight) {
          resolve(`${file.name}: Bild zu klein (mindestens ${minWidth}x${minHeight} Pixel erforderlich, aktuell: ${width}x${height} Pixel)`);
        } else if (width > maxWidth || height > maxHeight) {
          resolve(`${file.name}: Bild zu groß (maximal ${maxWidth}x${maxHeight} Pixel erlaubt, aktuell: ${width}x${height} Pixel)`);
        } else {
          resolve(null);
        }
      };

      img.onerror = () => {
        URL.revokeObjectURL(url);
        resolve(`${file.name}: Bilddatei konnte nicht gelesen werden`);
      };

      img.src = url;
    });
  };

  // Validate files before upload
  const validateFiles = async (files: FileList): Promise<string[]> => {
    const errors: string[] = [];

    // Check file count limit
    if (fieldFiles.length + files.length > maxFiles) {
      errors.push(`Maximal ${maxFiles} Dateien pro Feld erlaubt`);
    }

    // Check individual files
    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      // Check file size
      if (file.size > maxFileSize) {
        errors.push(`${file.name}: Datei zu groß (max. ${(maxFileSize / 1024 / 1024).toFixed(1)}MB)`);
      }

      // Check file type
      if (!ALLOWED_FILE_TYPES.includes(file.type)) {
        errors.push(`${file.name}: Dateityp nicht unterstützt (nur PDF, JPG, PNG, WebP)`);
      }

      // Check for duplicate filenames with fresh data
      if (await checkFileExistsWithRefresh(file.name)) {
        errors.push(`Eine Datei mit dem Namen "${file.name}" existiert bereits`);
      }

      // Check if file is currently being uploaded
      if (uploadingFiles.has(file.name)) {
        errors.push(`"${file.name}" wird bereits hochgeladen`);
      }

      // Validate image dimensions for building images
      const dimensionError = await validateImageDimensions(file);
      if (dimensionError) {
        errors.push(dimensionError);
      }
    }

    // Check total size
    const currentSize = fieldFiles.reduce((sum, file) => sum + file.size, 0);
    const newFilesSize = Array.from(files).reduce((sum, file) => sum + file.size, 0);
    if (currentSize + newFilesSize > maxTotalSize) {
      errors.push(`Gesamtgröße überschreitet ${(maxTotalSize / 1024 / 1024).toFixed(1)}MB`);
    }

    return errors;
  };

  // Handle file selection and upload
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    // Clear previous errors and show loading state for validation
    setUploadErrors([]);

    // Store file names for cleanup - do this early to ensure we have them
    const fileNames = Array.from(files).map(f => f.name);

    try {
      // Force refresh file list before validation to ensure we have the latest state
      await refreshFiles();

      // Validate files (now async due to image dimension validation)
      const validationErrors = await validateFiles(files);
      if (validationErrors.length > 0) {
        setUploadErrors(validationErrors);
        // Reset the file input to allow user to select different files
        e.target.value = '';
        return;
      }

      // Start upload process
      setIsUploading(true);

      // Track files being uploaded
      console.log('Adding files to uploading state:', fileNames);
      setUploadingFiles(prev => new Set([...prev, ...fileNames]));

      // Upload each file with individual error handling
      const uploadResults = await Promise.allSettled(
        Array.from(files).map(file => uploadSingleFile(file))
      );

      // Check for any failed uploads
      const failedUploads = uploadResults
        .map((result, index) => ({ result, file: files[index] }))
        .filter(({ result }) => result.status === 'rejected')
        .map(({ result, file }) => `${file.name}: ${(result as PromiseRejectedResult).reason.message}`);

      if (failedUploads.length > 0) {
        setUploadErrors(failedUploads);
        // Don't return here - still refresh to show any successful uploads
      }

      // Clear uploading files tracking immediately after upload completion
      console.log('Clearing files from uploading state:', fileNames);
      setUploadingFiles(prev => {
        const newSet = new Set(prev);
        fileNames.forEach(name => {
          console.log(`Removing ${name} from uploading state`);
          newSet.delete(name);
        });
        console.log('Remaining uploading files:', Array.from(newSet));
        return newSet;
      });

      // Refresh the file list with a simpler approach
      try {
        // Wait a moment for storage consistency
        await new Promise(resolve => setTimeout(resolve, 500));

        // Refresh the file list
        await refreshFiles();

        console.log('File list refreshed successfully after upload');
      } catch (refreshError) {
        console.warn('File list refresh failed:', refreshError);
        // Don't show error to user as uploads were successful
        // The file list will be updated on next page load or manual refresh
      }

      // Reset the file input
      e.target.value = '';
    } catch (error) {
      console.error('Upload error:', error);
      setUploadErrors([`Fehler beim Hochladen: ${error instanceof Error ? error.message : 'Unbekannter Fehler'}`]);
      // Reset the file input on error
      e.target.value = '';

      // Clear uploading files tracking on error as well
      setUploadingFiles(prev => {
        const newSet = new Set(prev);
        fileNames.forEach(name => newSet.delete(name));
        return newSet;
      });
    } finally {
      setIsUploading(false);
      setUploadProgress({});

      // Additional cleanup to ensure uploading files are cleared
      setUploadingFiles(prev => {
        const newSet = new Set(prev);
        fileNames.forEach(name => newSet.delete(name));
        return newSet;
      });
    }
  };

  // Upload a single file with enhanced error handling and verification
  const uploadSingleFile = async (file: File): Promise<void> => {
    if (!certificateId) throw new Error('Kein Zertifikat ausgewählt');

    // Get user ID
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('Nicht eingeloggt');

    const userId = user.user.id;
    const filePath = `${userId}/${certificateId}/${fieldName}_${file.name}`;

    // Check if file already exists before upload (with fresh data)
    try {
      const { data: existingFiles } = await supabase.storage
        .from('certificateuploads')
        .list(`${userId}/${certificateId}`, {
          search: `${fieldName}_${file.name}`
        });

      if (existingFiles && existingFiles.length > 0) {
        throw new Error(`Datei "${file.name}" existiert bereits`);
      }
    } catch (error) {
      // If it's our duplicate error, re-throw it
      if (error instanceof Error && error.message.includes('existiert bereits')) {
        throw error;
      }
      // Otherwise, log but continue (might be a permission issue checking)
      console.warn('Could not check for existing file:', error);
    }

    // Create upload with progress tracking and timeout
    const xhr = new XMLHttpRequest();
    const uploadTimeout = 60000; // 60 seconds timeout

    // Set up progress tracking
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable) {
        const percent = Math.round((event.loaded / event.total) * 100);
        setUploadProgress(prev => ({
          ...prev,
          [file.name]: percent
        }));
      }
    });

    // Create upload promise with timeout
    const uploadPromise = new Promise<void>((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        xhr.abort();
        reject(new Error(`Upload timeout für ${file.name}`));
      }, uploadTimeout);

      xhr.onreadystatechange = function() {
        if (xhr.readyState === XMLHttpRequest.DONE) {
          clearTimeout(timeoutId);
          if (xhr.status >= 200 && xhr.status < 300) {
            resolve();
          } else {
            reject(new Error(`Upload fehlgeschlagen für ${file.name} (Status: ${xhr.status})`));
          }
        }
      };

      xhr.onerror = function() {
        clearTimeout(timeoutId);
        reject(new Error(`Netzwerkfehler beim Upload von ${file.name}`));
      };

      xhr.onabort = function() {
        clearTimeout(timeoutId);
        reject(new Error(`Upload von ${file.name} abgebrochen`));
      };
    });

    // Get presigned URL with retry
    let uploadData;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        const { data, error: uploadError } = await supabase.storage
          .from('certificateuploads')
          .createSignedUploadUrl(filePath);

        if (uploadError) {
          throw new Error(`Fehler beim Erstellen der Upload-URL: ${uploadError.message}`);
        }

        if (!data) {
          throw new Error('Fehler beim Erstellen der Upload-URL: Keine Daten erhalten');
        }

        uploadData = data;
        break;
      } catch (error) {
        retryCount++;
        if (retryCount >= maxRetries) {
          throw error;
        }
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
      }
    }

    // Ensure uploadData was successfully obtained
    if (!uploadData) {
      throw new Error('Fehler beim Erstellen der Upload-URL: Keine Upload-Daten erhalten');
    }

    // Configure and send request
    xhr.open('PUT', uploadData.signedUrl);
    xhr.send(file);

    // Wait for upload to complete
    await uploadPromise;

    // Verify upload was successful by checking if file exists
    try {
      const { data: verifyFiles } = await supabase.storage
        .from('certificateuploads')
        .list(`${userId}/${certificateId}`, {
          search: `${fieldName}_${file.name}`
        });

      if (!verifyFiles || verifyFiles.length === 0) {
        throw new Error(`Upload-Verifikation fehlgeschlagen für ${file.name}`);
      }
    } catch (error) {
      console.warn('Could not verify upload:', error);
      // Don't throw here as the upload might have succeeded
    }
  };

  // Handle file deletion - memoized to prevent unnecessary re-renders
  const handleDeleteFile = useCallback(async (file: StorageFileInfo) => {
    try {
      const success = await deleteFileFromStorage(file.path);
      if (!success) {
        setUploadErrors(['Fehler beim Löschen der Datei']);
      }
    } catch (error) {
      console.error('Delete error:', error);
      setUploadErrors([`Fehler beim Löschen: ${error instanceof Error ? error.message : 'Unbekannter Fehler'}`]);
    }
  }, [deleteFileFromStorage]);

  // Calculate current usage - memoized to prevent recalculation on every render
  const currentSize = useMemo(() =>
    fieldFiles.reduce((sum, file) => sum + file.size, 0),
    [fieldFiles]
  );
  const currentSizeMB = useMemo(() =>
    (currentSize / 1024 / 1024).toFixed(1),
    [currentSize]
  );
  const maxSizeMB = useMemo(() =>
    (maxTotalSize / 1024 / 1024).toFixed(1),
    [maxTotalSize]
  );

  return (
    <div className="mb-6 p-4 border border-gray-200 rounded-lg">
      <label className="block text-sm font-medium text-gray-700 mb-3">
        {label} {required && <span className="text-red-500">*</span>}
      </label>

      {/* File Upload Button */}
      <div className="mb-4">
        <input
          type="file"
          id={`${fieldName}-upload`}
          accept={accept}
          multiple={multiple}
          onChange={handleFileChange}
          className="hidden"
          disabled={isUploading || fieldFiles.length >= maxFiles}
        />
        <label
          htmlFor={`${fieldName}-upload`}
          className={`inline-flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors cursor-pointer ${
            isUploading || fieldFiles.length >= maxFiles
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-green-600 text-white hover:bg-green-700'
          }`}
        >
          {isUploading ? (
            <>
              <svg className="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Wird hochgeladen...
            </>
          ) : (
            <>
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              {fieldFiles.length === 0 ? 'Dateien auswählen' : 'Weitere Dateien hinzufügen'}
            </>
          )}
        </label>

        {fieldFiles.length >= maxFiles && (
          <p className="text-xs text-gray-500 mt-1">
            Maximum von {maxFiles} Dateien erreicht
          </p>
        )}
      </div>

      {/* File Size Info */}
      {fieldFiles.length > 0 && (
        <div className="mb-4 text-xs text-gray-600">
          <span>Gesamtgröße: {currentSizeMB} MB / {maxSizeMB} MB</span>
          <span className="ml-4">Dateien: {fieldFiles.length} / {maxFiles}</span>
        </div>
      )}

      {/* Info Message for Building Images */}
      {fieldName === 'gebaeudebild' && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <h4 className="text-sm font-medium text-blue-800 mb-1">Hinweise zum Gebäudebild:</h4>
          <ul className="text-xs text-blue-700 space-y-1">
            <li>• Mindestauflösung: 360 x 240 Pixel (entspricht 6cm x 4cm bei 60 Pixel/cm)</li>
            <li>• Maximalauflösung: 4000 x 3000 Pixel</li>
            <li>• Unterstützte Formate: JPG, PNG, WebP</li>
            <li>• Maximale Dateigröße: {(maxFileSize / 1024 / 1024).toFixed(1)} MB</li>
          </ul>
        </div>
      )}

      {/* Info Message for Floor Plans */}
      {fieldName === 'grundriss' && (
        <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
          <h4 className="text-sm font-medium text-green-800 mb-1">Hinweise zu Grundrissen:</h4>
          <div className="text-xs text-green-700 space-y-1">
            <p>Folgende Unterlagen zur Wohnfläche können hochgeladen werden:</p>
            <ul className="list-disc list-inside ml-2 space-y-1">
              <li>Wohnflächenberechnung</li>
              <li>Flächenaufmaß</li>
              <li>vermasste Grundrisse oder Skizzen</li>
              <li>sonstige Unterlagen, aus denen sich die Wohnfläche ermitteln lässt</li>
            </ul>
            <p className="mt-2 font-medium">Bitte nur vollständig lesbare Unterlagen hochladen um Rückfragen zu vermeiden.</p>
            <p>• Unterstützte Formate: PDF, JPG, PNG</p>
            <p>• Maximale Dateigröße: {(maxFileSize / 1024 / 1024).toFixed(1)} MB pro Datei</p>
            <p>• Maximal {maxFiles} Dateien</p>
          </div>
        </div>
      )}

      {/* Error Messages */}
      {uploadErrors.length > 0 && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <h4 className="text-sm font-medium text-red-800 mb-1">Fehler beim Upload:</h4>
          <ul className="text-xs text-red-700 space-y-1">
            {uploadErrors.map((error, index) => (
              <li key={index}>• {error}</li>
            ))}
          </ul>
          {fieldName === 'gebaeudebild' && uploadErrors.some(error => error.includes('Pixel')) && (
            <div className="mt-2 pt-2 border-t border-red-300">
              <p className="text-xs text-red-600 font-medium">
                Tipp: Verwenden Sie ein Bildbearbeitungsprogramm, um die Bildgröße anzupassen, oder wählen Sie ein anderes Bild aus.
              </p>
            </div>
          )}
        </div>
      )}

      {/* Upload Progress for files currently being uploaded */}
      {uploadingFiles.size > 0 && (
        <div className="mb-4 space-y-2">
          <h4 className="text-sm font-medium text-gray-700">
            Wird hochgeladen ({uploadingFiles.size})
          </h4>
          {Array.from(uploadingFiles).map((fileName) => (
            <div key={fileName} className="flex items-center space-x-3 p-2 bg-blue-50 border border-blue-200 rounded-md">
              <div className="flex-shrink-0">
                <svg className="animate-spin w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">{fileName}</p>
                {uploadProgress[fileName] !== undefined && (
                  <div className="mt-1">
                    <div className="bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${uploadProgress[fileName]}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">{uploadProgress[fileName]}%</p>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Files List */}
      {fieldFiles.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">
            Dateien ({fieldFiles.length})
          </h4>
          {fieldFiles.map((file) => (
            <FileItem
              key={file.path}
              file={file}
              onDelete={() => handleDeleteFile(file)}
              isDeleting={isDeleting === file.path}
              uploadProgress={uploadProgress[file.originalName]}
            />
          ))}
        </div>
      )}
    </div>
  );
});

// File item component - memoized to prevent unnecessary re-renders
const FileItem = memo(({
  file,
  onDelete,
  isDeleting,
  uploadProgress
}: {
  file: StorageFileInfo;
  onDelete: () => void;
  isDeleting: boolean;
  uploadProgress?: number;
}) => {
  const isUploading = uploadProgress !== undefined && uploadProgress > 0 && uploadProgress < 100;

  return (
    <div className="flex items-center justify-between p-2 bg-gray-50 rounded border">
      <div className="flex-1 min-w-0">
        <div className="flex items-center space-x-2">
          <div className="flex-shrink-0">
            {file.fileType === 'pdf' ? (
              <svg className="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
              </svg>
            ) : (
              <svg className="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
              </svg>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium text-gray-900 truncate">{file.originalName}</p>
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                Gespeichert
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <p className="text-xs text-gray-500">{(file.size / 1024).toFixed(1)} KB</p>
              <FileWithSignedUrl
                path={file.path}
                label="Anzeigen"
                className="text-xs"
              />
            </div>
          </div>
        </div>

        {/* Progress Bar for uploading files */}
        {isUploading && (
          <div className="mt-2">
            <div className="w-full bg-gray-200 rounded-full h-1.5">
              <div
                className="bg-green-600 h-1.5 rounded-full transition-all duration-300"
                style={{ width: `${uploadProgress}%` }}
              ></div>
            </div>
            <p className="text-xs text-gray-500 mt-1">Wird hochgeladen... {uploadProgress}%</p>
          </div>
        )}
      </div>

      {/* Delete Button */}
      <button
        type="button"
        onClick={onDelete}
        className="ml-2 p-1 text-red-500 hover:text-red-700 transition-colors"
        disabled={isDeleting || isUploading}
        title="Datei löschen"
      >
        {isDeleting ? (
          <div className="w-4 h-4 animate-spin rounded-full border-2 border-red-500 border-t-transparent"></div>
        ) : (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        )}
      </button>
    </div>
  );
});
