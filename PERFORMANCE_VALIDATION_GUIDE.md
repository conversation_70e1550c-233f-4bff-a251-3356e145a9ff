# Performance Optimization Validation Guide

## Overview

This guide provides step-by-step instructions to validate the performance optimizations implemented for the `useCertificateFiles` hook and related components.

## Optimizations Implemented

### 1. Hook Optimization - Enhanced Caching
- **User Query**: `staleTime: Infinity` (was 1 hour)
- **File Query**: `staleTime: 5 minutes` (was 2 minutes)
- **Refetch Prevention**: Added `refetchOnWindowFocus: false`, `refetchOnMount: false`, `refetchOnReconnect: false`
- **Retry Optimization**: Reduced retries and increased delays

### 2. Component Memoization
- **DirectoryBasedFileUpload**: Wrapped with `React.memo`
- **FileItem**: Wrapped with `React.memo`
- **File Components**: Already memoized in ZusammenfassungPage
- **Expensive Calculations**: Wrapped with `useMemo`
- **Event Handlers**: Wrapped with `useCallback`

### 3. Global Query Configuration
- **Cache Time**: Increased to 15 minutes (was 10 minutes)
- **Retry Delay**: Increased to 1.5 seconds
- **Background Refetching**: Disabled

## Validation Steps

### Step 1: Baseline Measurement

Before testing, open browser DevTools and run:

```javascript
// In browser console
window.performanceMonitor = window.performanceMonitor || { reset: () => {}, logPerformanceSummary: () => {} };
window.performanceMonitor.reset();
```

### Step 2: Test ZusammenfassungPage Performance

1. **Navigate to ZusammenfassungPage**
2. **Open Browser DevTools Console**
3. **Look for performance logs**:
   ```
   📁 useCertificateFiles hook instance created/changed: {...}
   📊 Hook Performance: useCertificateFiles called X times
   ```

**Expected Results:**
- ✅ **Single Hook Call**: Should see only 1 `useCertificateFiles` call
- ✅ **Cache Hits**: User queries should hit cache (no network requests)
- ✅ **Fast Rendering**: File components should render without delays

### Step 3: Test DirectoryBasedFileUpload Performance

1. **Navigate through form pages** (Objektdaten → Gebäudedetails1 → Heizung → Verbrauch)
2. **Monitor console logs** for hook calls
3. **Check React Query DevTools** (if enabled)

**Expected Results:**
- ✅ **Reduced Hook Calls**: Should see 6 total calls (one per component)
- ✅ **Cache Performance**: File queries should have high cache hit rates
- ✅ **No Unnecessary Re-renders**: Components should not re-render on tab switching

### Step 4: Window Focus Test

1. **Open ZusammenfassungPage**
2. **Switch to another browser tab**
3. **Switch back to the application**
4. **Monitor network tab** for unnecessary requests

**Expected Results:**
- ✅ **No Refetches**: Should see no network requests on window focus
- ✅ **Cached Data**: All data should load from cache
- ✅ **Fast Response**: Page should respond immediately

### Step 5: Memory Usage Test

1. **Open Chrome DevTools → Performance tab**
2. **Start recording**
3. **Navigate through multiple pages**
4. **Stop recording and analyze**

**Expected Results:**
- ✅ **Stable Memory**: No memory leaks from excessive re-renders
- ✅ **Efficient Calculations**: Memoized calculations should not recalculate unnecessarily
- ✅ **Component Stability**: File components should not re-mount unnecessarily

## Performance Metrics to Track

### Hook Call Metrics
```javascript
// Expected hook call counts:
// - ZusammenfassungPage: 1 call
// - DirectoryBasedFileUpload instances: 6 calls
// - Total: 7 calls (down from 8+ previously)
```

### Query Performance Metrics
```javascript
// Expected cache hit rates:
// - User queries: 90%+ (due to staleTime: Infinity)
// - File queries: 70%+ (due to increased staleTime)
// - Overall: Significant reduction in network requests
```

### Component Re-render Metrics
```javascript
// Expected re-render behavior:
// - File components: Only re-render when data changes
// - Upload components: Only re-render when files change
// - Summary components: Minimal re-renders due to memoization
```

## Testing Commands

### Run Performance Tests
```bash
npm test src/utils/__tests__/performanceOptimization.test.ts
```

### Run Component Tests
```bash
npm test src/components/ui/__tests__/DirectoryBasedFileUpload.test.ts
```

### Enable React Query DevTools
Add to your development environment:
```typescript
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// In App.tsx (already added)
<ReactQueryDevtools initialIsOpen={false} />
```

## Troubleshooting

### High Hook Call Count
If you still see 8+ hook calls:
1. Check if multiple pages are rendering simultaneously
2. Verify that components are properly memoized
3. Look for unnecessary re-renders in parent components

### Poor Cache Performance
If cache hit rates are low:
1. Verify `staleTime` settings in queries
2. Check for cache invalidation calls
3. Ensure query keys are stable

### Memory Issues
If memory usage is high:
1. Check for memory leaks in useEffect cleanup
2. Verify that event listeners are properly removed
3. Look for circular references in cached data

## Success Criteria

The optimization is successful if:

- ✅ **Hook Calls**: Reduced from 8+ to 7 or fewer
- ✅ **Cache Hit Rate**: User queries >90%, File queries >70%
- ✅ **Window Focus**: No unnecessary refetches on tab switching
- ✅ **Memory Usage**: Stable memory usage during navigation
- ✅ **User Experience**: Faster page loads and smoother interactions

## Monitoring in Production

For production monitoring, consider:

1. **Performance Metrics**: Track hook call counts and cache hit rates
2. **User Experience**: Monitor page load times and interaction delays
3. **Error Rates**: Ensure optimizations don't introduce errors
4. **Memory Usage**: Monitor for memory leaks over time

## Next Steps

After validation:

1. **Remove Debug Logging**: Clean up console.log statements
2. **Document Changes**: Update component documentation
3. **Monitor Production**: Set up performance monitoring
4. **Consider Further Optimizations**: Look for additional improvement opportunities
